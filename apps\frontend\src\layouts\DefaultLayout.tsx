import { Button } from "@snap/design-system";
import { Check, Save } from "lucide-react";
import { useState } from "react";
import { Outlet } from "react-router";
import BreadcrumbNavigation from "~/components/Breadcrumbs";
import Header from "~/components/Header";
import Toolbar from "~/components/Toolbar";
import { BreadcrumbItem } from "~/types/global";
import { useLocation } from "react-router";
import { useReportActionsWithAutoSave } from "~/store/reportDetailStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

const DefaultLayout = () => {
  const actions = useReportActionsWithAutoSave();
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  // TODO - Remover e adionar uma lógica menos gambiarra
  const location = useLocation();
  const reportPath = "report"
  const isReportPage = location.pathname.includes(reportPath);
  const isAutoSaving = actions.isPendingSave();

  return (
    <>
      <Header />
      <div className="flex justify-between px-8">
        <BreadcrumbNavigation breadcrumbs={breadcrumbs} />
        {
          isReportPage && (
            <Button disabled={isAutoSaving} className="uppercase !cursor-auto" icon={isAutoSaving ?
              <AiOutlineLoading3Quarters size={16} className="animate-spin mr-2" /> :
              <Check size={16} className="mr-2"/>} iconPosition="right">autosave ativo</Button>
          )
        }

      </div>
      <main className="flex flex-col min-h-screen">
        <div className="w-full px-8 py-4">
          <Toolbar />
        </div>
        <div className="flex-1 w-full flex flex-col min-h-0">
          <Outlet context={{ setBreadcrumbs }} />
        </div>
      </main>
    </>
  );
};

export default DefaultLayout;
